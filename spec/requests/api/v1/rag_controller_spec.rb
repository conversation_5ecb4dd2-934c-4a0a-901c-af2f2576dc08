# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::RagController', type: :request do
  let(:admin) { create(:user, name: 'Admin User', role: 'admin') }
  let(:regular_user) { create(:user, name: 'Regular User', role: 'student') }
  let(:dummy_file_url) { 'https://storage.api.com/files/dummy.pdf' }
  let(:rag_api_key) { 'test_rag_api_key' }

  before do
    org = Organization.default_organization
    org.internal_config['rag_api_key'] = Encryptor::Cipher.encrypt(rag_api_key, version: 3)
    org.save(validate: false)
  end

  describe 'POST /api/v1/rag/upload' do
    let(:upload_params) do
      {
        file_url: dummy_file_url,
        rag_api_key: rag_api_key
      }
    end

    context 'when user is admin' do
      before do
        post '/api/v1/rag/upload', upload_params,
             as_user(admin).merge({ 'RAG-API-KEY' => rag_api_key })
      end

      it 'creates a new RAG document' do
        expect_response(:created)
        expect(response_data[:id]).to be_truthy
        expect(response_data[:status]).to eq('pending')
        expect(response_data[:filename]).to eq('dummy.pdf')
      end
    end

    context 'when user is not admin' do
      it 'returns forbidden' do
        post '/api/v1/rag/upload', upload_params,
             as_user(regular_user).merge({ 'RAG-API-KEY' => rag_api_key })
        expect_response(:forbidden)
      end
    end

    context 'when rag_api_key is invalid' do
      it 'returns forbidden response' do
        post '/api/v1/rag/upload', upload_params,
             as_user(admin).merge({ 'RAG-API-KEY' => 'invalid_key' })
        expect_response(:forbidden)
      end
    end

    context 'when file_url is missing' do
      it 'returns unprocessable_entity response' do
        post '/api/v1/rag/upload', {}, as_user(admin).merge({ 'RAG-API-KEY' => rag_api_key })
        expect_response(:unprocessable_entity)
      end
    end

    context 'when file_url is invalid' do
      it 'returns unprocessable_entity response' do
        post '/api/v1/rag/upload', upload_params.merge(file_url: {}),
             as_user(admin).merge({ 'RAG-API-KEY' => rag_api_key })
        expect_response(:unprocessable_entity)
      end
    end
  end

  describe 'PATCH /api/v1/rag/:id/update_status' do
    let!(:rag_document) { create(:rag_uploaded_document, status: 'pending') }
    let(:update_params) do
      {
        status: 'done'
      }
    end

    context 'when rag_api_key is valid' do
      before do
        patch "/api/v1/rag/#{rag_document.id}/update_status", update_params,
              { 'RAG-API-KEY' => rag_api_key }
      end

      it 'updates the RAG document status' do
        expect_response(:ok)
        expect(response_data[:id]).to eq(rag_document.id)
        expect(response_data[:status]).to eq('done')
      end
    end

    context 'when rag_api_key is invalid' do
      it 'returns forbidden response' do
        patch "/api/v1/rag/#{rag_document.id}/update_status",
              update_params, { 'RAG-API-KEY' => 'invalid_key' }
        expect_response(:forbidden)
      end
    end

    context 'when document id does not exist' do
      it 'returns not_found response' do
        patch '/api/v1/rag/-999999/update_status', update_params, { 'RAG-API-KEY' => rag_api_key }
        expect_response(:not_found)
      end
    end

    context 'when status is invalid' do
      it 'returns unprocessable_entity response' do
        patch "/api/v1/rag/#{rag_document.id}/update_status",
              update_params.merge(status: 'invalid_status'), { 'RAG-API-KEY' => rag_api_key }
        expect_response(:unprocessable_entity)
      end
    end

    context 'when updating with error message' do
      let(:error_params) do
        {
          status: 'failed'
        }
      end

      it 'updates status and error message' do
        patch "/api/v1/rag/#{rag_document.id}/update_status", error_params,
              { 'RAG-API-KEY' => rag_api_key }
        expect_response(:ok)
        expect(response_data[:status]).to eq('failed')
      end
    end

    context 'when updating with metadata' do
      let(:metadata_params) do
        {
          status: 'done',
          rag_api_key: rag_api_key,
          metadata: {
            input_tokens: 1000,
            output_tokens: 500
          }
        }
      end

      it 'updates status and metadata' do
        patch "/api/v1/rag/#{rag_document.id}/update_status", metadata_params,
              { 'RAG-API-KEY' => rag_api_key }
        expect_response(:ok)
        expect(response_data[:status]).to eq('done')
        expect(response_data[:metadata][:input_tokens]).to eq(1000)
        expect(response_data[:metadata][:output_tokens]).to eq(500)
      end
    end
  end

  describe 'GET /api/v1/rag/list' do
    let!(:rag_document_1) { create(:rag_uploaded_document, status: 'done', created_at: 1.day.ago) }
    let!(:rag_document_2) do
      create(:rag_uploaded_document, status: 'pending', created_at: 2.days.ago)
    end
    let!(:rag_document_3) do
      create(:rag_uploaded_document, status: 'failed', created_at: 3.days.ago)
    end

    context 'when user is admin' do
      before do
        get '/api/v1/rag/list', {}, as_user(admin).merge({ 'RAG-API-KEY' => rag_api_key })
      end

      it 'returns list of RAG documents' do
        expect_response(:ok)
        expect(response_data).to be_an(Array)
        expect(response_data.length).to eq(3)
      end

      it 'returns documents in correct format' do
        first_document = response_data.first
        expect(first_document[:id]).to be_truthy
        expect(first_document[:status]).to be_truthy
        expect(first_document[:total_pages]).to be_truthy
        expect(first_document[:filename]).to be_truthy
      end

      it 'returns documents sorted by created_at desc' do
        document_ids = response_data.pluck(:id)
        expect(document_ids.size).to eq(3)
      end
    end

    context 'when user is not admin' do
      it 'returns forbidden' do
        get '/api/v1/rag/list', {}, as_user(regular_user).merge({ 'RAG-API-KEY' => rag_api_key })
        expect_response(:forbidden)
      end
    end

    context 'when rag_api_key is invalid' do
      it 'returns forbidden response' do
        get '/api/v1/rag/list', {}, as_user(admin).merge({ 'RAG-API-KEY' => 'invalid_key' })
        expect_response(:forbidden)
      end
    end

    context 'when filtering by status' do
      it 'returns only documents with specified status' do
        get '/api/v1/rag/list', { status: 'done' },
            as_user(admin).merge({ 'RAG-API-KEY' => rag_api_key })
        expect_response(:ok)
        expect(response_data.length).to eq(1)
        expect(response_data.first[:status]).to eq('done')
      end
    end
  end
end
