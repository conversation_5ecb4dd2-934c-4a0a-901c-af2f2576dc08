# frozen_string_literal: true

module Api
  module V1
    class RagController < ApiController
      authorize_auth_token! :public, only: %i[update_status retrieve]
      authorize_auth_token! %w[admin], only: %i[upload list]

      def upload
        input = ::V1::RagUploadInput.new(request_body)
        validate! input, capture_failure: true

        result = service.upload(input.output.merge(rag_api_key: rag_api_key))
        render_json result, status: :created, use: :created_format
      end

      def update_status
        input = ::V1::RagUploadUpdateInput.new(request_body)
        validate! input, capture_failure: true

        result = service.update_status(input.output.merge(id: params[:id],
                                                          rag_api_key: rag_api_key))
        render_json result
      end

      def retrieve
        input = ::V1::RagRetrieveInput.new(request_body)
        validate! input, capture_failure: true

        result = service.retrieve(input.output.merge(rag_api_key: rag_api_key))
        render_json_array result, use: :retrieve_format
      end

      def list
        result = service.list(query_params.merge(rag_api_key: rag_api_key))
        render_json_array result
      end

      private

      def rag_api_key
        request.headers['RAG-API-KEY']
      end

      def service
        @service ||= RagService.new(current_user)
      end

      def default_output
        ::V1::RagDocumentOutput
      end
    end
  end
end
