# frozen_string_literal: true

class FanOutVideoAnalyzerWorker < ApplicationWorker
  def perform
    list_runs = []

    Organization.unscoped.kept.pluck(:scheme).uniq.each do |scheme|
      Apartment::Tenant.switch(scheme) do
        video_analyzer_assistant = Ai::Assistant.where(purpose: analyzer_prompt)
        next unless video_analyzer_assistant.present?

        ai_assistant_ids = video_analyzer_assistant.pluck(:id)
        in_progress_runs = Ai::AssistantRun.where(ai_assistant_id: ai_assistant_ids,
                                                  status: :in_progress)
        next if in_progress_runs.present?

        early_run = Ai::AssistantRun.where(
          ai_assistant_id: ai_assistant_ids,
          status: :queued
        ).order(created_at: :asc).first

        next unless early_run.present?

        list_runs << {
          scheme: scheme,
          id: early_run.id,
          created_at: early_run.created_at
        }
      end
    end

    earlier_run = list_runs.min_by { |run| run[:created_at] }
    return unless earlier_run.present?

    Apartment::Tenant.switch(earlier_run[:scheme]) do
      Ai::VideoAnalyzer::MainWorker.perform_async(earlier_run[:id])
    end
  end

  def analyzer_prompt
    org_internal_config['video_analyzer_prompts'] || []
  end
end
