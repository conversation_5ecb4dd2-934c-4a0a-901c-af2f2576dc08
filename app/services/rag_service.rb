# frozen_string_literal: true

require 'qdrant'

class RagService < AppService
  def initialize(user = nil)
    @user = user
    @rag_document_repo = RagUploadedDocuments.new
  end

  def upload(params)
    payload_rag_api_key = params.delete(:rag_api_key)
    authorize! rag_api_key == payload_rag_api_key,
               on_error: 'Invalid API key for access this resource'

    rag_document = RagUploadedDocument.create!(params.merge(status: 'pending'))
    worker_params = JSON.dump(
      {
        document_id: rag_document.id,
        file_url: rag_document.file_url
      }
    )
    RagDocumentProcessingWorker.perform_async(worker_params)

    rag_document
  end

  def update_status(params)
    current_tenant = Apartment::Tenant.current

    method = "rag_update_status_#{current_tenant}"
    identifier = params[:id]

    payload_rag_api_key = params.delete(:rag_api_key)

    authorize! rag_api_key == payload_rag_api_key,
               on_error: 'Invalid API key for access this resource'

    transaction_with_advisory_lock(method, identifier) do
      rag_document = RagUploadedDocument.find(params[:id])
      rag_document.update!(params)

      rag_document.reload
    end
  end

  def retrieve(params)
    query = params[:query]
    top_k = params[:top_k] || 5
    score_threshold = params[:score_threshold]
    filters = params[:filters]

    payload_rag_api_key = params.delete(:rag_api_key)
    authorize! rag_api_key == payload_rag_api_key,
               on_error: 'Invalid API key for access this resource'

    # Start timing the query
    start_time = Time.current

    # 1. Generate embedding using Gemini REST API with Faraday
    gemini_api_key = ENV['GEMINI_API_KEY']
    gemini_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-embedding-001:embedContent?key=#{gemini_api_key}"

    request_body = {
      model: 'models/gemini-embedding-001',
      content: {
        parts: [
          { text: query }
        ]
      }
    }

    response = Faraday.post(gemini_url) do |req|
      req.headers['Content-Type'] = 'application/json'
      req.body = request_body.to_json
    end

    embedding_response = JSON.parse(response.body)
    embedding = embedding_response.dig('embedding',
                                       'values') || embedding_response.dig('data', 0, 'embedding')
    raise 'Failed to get embedding from Gemini' unless embedding

    qdrant_url = ENV['QDRANT_URL']
    qdrant_api_key = ENV['QDRANT_API_KEY']
    collection_name = ENV['QDRANT_COLLECTION']

    client = ::Qdrant::Client.new(
      url: qdrant_url,
      api_key: qdrant_api_key
    )

    search_params = {
      vector: embedding,
      limit: top_k,
      with_payload: true,
      collection_name: collection_name
    }
    search_params[:score_threshold] = score_threshold if score_threshold

    if filters.present?
      document_id_filters = filters['document_id'].map do |doc_id|
        {
          'key': 'metadata.document_id',
          'match': {
            'value': doc_id
          }
        }
      end

      search_params[:filter] = {
        'should': document_id_filters
      }
    end

    begin
      result_points = client.points.search(**search_params)
      result_points = result_points&.with_indifferent_access&.dig('result')
    rescue StandardError => e
      raise "Qdrant error: #{e.message}"
    end

    # Calculate query time
    end_time = Time.current
    query_time_in_ms = ((end_time - start_time) * 1000).round(2)

    # Transform Qdrant results to the expected format
    transformed_results = result_points.map do |item|
      {
        id: item['id'],
        score: item['score'],
        metadata: item['payload']&.dig('metadata') || {},
        text: item['payload']&.dig('text') || ''
      }
    end

    # Store query retrieval for analytics
    RagQueryRetrieval.create!(
      query: query,
      top_k: top_k,
      score_threshold: score_threshold,
      filters: search_params[:filter] || {},
      results_count: transformed_results.count,
      query_time_in_ms: query_time_in_ms
    )

    transformed_results
  end

  def list(params = {})
    payload_rag_api_key = params.delete(:rag_api_key)
    authorize! rag_api_key == payload_rag_api_key,
               on_error: 'Invalid API key for access this resource'

    @rag_document_repo.filter(params)
  end

  private

  def dag_name
    ENV['RAG_DAG_NAME']
  end
end
