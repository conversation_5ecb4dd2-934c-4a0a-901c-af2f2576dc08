# frozen_string_literal: true

require_relative 'routes_helpers'

Rails.application.routes.draw do
  extend RoutesHelpers

  namespace :api do
    namespace :v1 do
      resources :skills, only: %i[index create]
      resources :portfolios, only: %i[index show create update destroy]
      resources :certificates, only: %i[index show create update]
      resources :user_skills, only: %i[index create update destroy]
      resources :survey_templates do
        resources :survey_components
      end
      resources :surveys do
        collection do
          put ':id/start', to: 'surveys#start'
          get ':id/result', to: 'surveys#result'
          patch ':id/remind_me_later', to: 'surveys#remind_me_later'
        end
      end

      get 'user_surveys/ongoing', to: 'user_surveys#ongoing_surveys'
      get 'user_surveys/:survey_id', to: 'user_surveys#show'
      put 'user_surveys/:survey_id/questions/:component_id', to: 'user_surveys#upsert_answer'
      resources :survey_answers, only: %i[destroy]

      resources :homework_questions do
        resources :answer_feedbacks, only: %i[create update destroy]
        resources :homework_options, only: %i[create update destroy]
      end

      post 'homework_questions/import', to: 'homework_questions#import'
      patch 'homework_questions', to: 'homework_questions#bulk_update'

      resources :projects, only: %i[index show create update] do
        post 'submit', to: 'projects#submit'
      end

      resources :answers, only: %i[index create update destroy]
      resources :answer_options, only: %i[index create] do
        delete ':homework_question_id', to: 'answer_options#destroy', on: :collection
      end

      resources :courses do
        collection do
          get 'recommendations', to: 'recommendations'
          get ':category/latest', to: 'courses#latest'
          get ':course_id/certificates', to: 'certificates#index_internal'
          get ':course_id/assignment_result', to: 'courses#assignment_result'
          get ':course_id/session_result', to: 'courses#session_result'
          get ':course_id/gamification_result', to: 'courses#gamification_result'
          get ':course_id/rtc_questionnaire_result', to: 'courses#rtc_questionnaire_result'
          get ':course_id/completion_rate_result', to: 'courses#completion_rate_result'
          get ':course_id/assessment_answer_result', to: 'courses#assessment_answer_result'
          get ':course_id/assignment_details', to: 'courses#assignment_details'
          get ':course_id/weeks', to: 'courses#week_index'
          patch ':course_id/complete_onboard', to: 'courses#complete_onboard'
          post ':course_id/bootcamp_trial_mail', to: 'courses#bootcamp_trial_mail'
        end

        post 'enroll', to: 'enroll'
        post 'duplicate', to: 'duplicate'
      end

      resources :module_sessions do
        collection do
          get ':id/result', to: 'module_sessions#result'
          patch ':id/recalculate_scores', to: 'module_sessions#recalculate_scores'
        end
      end

      patch 'module_sessions', to: 'module_sessions#bulk_update'

      resources :user_module_sessions, only: %i[update]
      resources :user_courses, only: %i[index destroy] do
        collection do
          get 'detail/:course_id', to: 'user_courses#show'
          patch 'grant_role/:id', to: 'user_courses#grant_role'
          patch 'revoke_role/:id', to: 'user_courses#revoke_role'
          patch 'grant_astor/:course_id', to: 'user_courses#grant_astor'
        end
      end
      resources :course_modules
      patch 'course_modules', to: 'course_modules#bulk_update'

      post 'auth/login', to: 'authentication#authenticate'
      get  'auth', to: 'authentication#show'
      post 'auth/login/google', to: 'authentication#authenticate_google'
      post 'auth/login/partner', to: 'authentication#authenticate_partner'
      post 'auth/forgot_password', to: 'authentication#forgot_password'
      post 'auth/reset_password', to: 'authentication#reset_password'
      post 'signup', to: 'users#create'
      post 'signup/google', to: 'users#create_with_provider'
      get  'uploads/presign', to: 'uploads#presign'
      get  'uploads/presign_no_auth', to: 'uploads#presign_no_auth'
      post 'rag/upload', to: 'rag#upload'
      patch 'rag/:id/update_status', to: 'rag#update_status'
      post 'rag/retrieve', to: 'rag#retrieve'
      get 'rag/list', to: 'rag#list'
      get  'downloads/presigned_url', to: 'downloads#presigned_url'
      get  'organizations/:identifier', to: 'organizations#identify',
                                        constraints: { identifier: %r{[^/]+} }
      post 'organizations', to: 'organizations#setup'
      patch 'organizations/:identifier', to: 'organizations#update'

      resources :users do
        collection do
          get ':user_id/educations', to: 'educations#index'
          post 'educations', to: 'educations#create'
          patch 'educations', to: 'educations#update'
          delete 'educations/:id', to: 'educations#destroy'

          get ':id/experiences', to: 'experiences#index'
          post 'experiences', to: 'experiences#create'
          patch 'experiences', to: 'experiences#update'
          delete 'experiences/:id', to: 'experiences#destroy'

          get ':id/courses', to: 'users#index_courses'
          post 'external_courses', to: 'users#add_external_course'
          patch 'external_courses', to: 'users#update_external_course'
          delete 'external_courses/:id', to: 'users#remove_external_course'

          post 'advanced', to: 'users#index_advanced'
          get 'identify', to: 'users#identify'

          get ':id/achievements', to: 'achievements#index'
          post 'achievements', to: 'achievements#create'
          patch 'achievements', to: 'achievements#update'
          delete 'achievements/:id', to: 'achievements#destroy'

          get ':id/career_path_enrolled_product', to: 'users#career_path_enrolled_products'
          patch '/grant_tutor_role', to: 'users#grant_tutor_role'
          post 'bulk_create', to: 'users#bulk_create'
          post '/verify_token', to: 'user_tokens#verify'
          post '/send_verification', to: 'user_tokens#send_verification'

          post 'points', to: 'users#update_points'
          get 'digital_cv_progress', to: 'users#digital_cv_progress'
          get 'talent_pool', to: 'users#talent_pool'
          get 'talent_pool/download', to: 'users#download_talent_pool'
          post 'change_email', to: 'users#change_email'

          post 'imports', to: 'user_import_results#create'
          get 'imports/:id', to: 'user_import_results#show'
          put 'imports/:id/save', to: 'user_import_results#save'

          post 'leadership_experiences', to: 'leadership_experiences#create'
          get ':user_id/leadership_experiences', to: 'leadership_experiences#index'
          patch 'leadership_experiences/:id', to: 'leadership_experiences#update'
          delete 'leadership_experiences/:id', to: 'leadership_experiences#destroy'

          get ':user_id/organization_experiences', to: 'organization_experiences#index'
          post 'organization_experiences', to: 'organization_experiences#create'
          patch 'organization_experiences/:id', to: 'organization_experiences#update'
          delete 'organization_experiences/:id', to: 'organization_experiences#destroy'
        end
      end

      resources :attendance_histories, only: %i[index create update] do
        collection do
          get 'summary', to: 'attendance_histories#summary'
          patch 'student_reminder/:id', to: 'attendance_histories#send_reminder'
        end
      end

      resources :assignment_scores, only: %i[index show create update] do
        collection do
          get 'summary', to: 'assignment_scores#summary'
          get 'weekly_summary', to: 'assignment_scores#weekly_summary'
          get 'tutor_list', to: 'assignment_scores#tutor_list'
          get 'leaderboard', to: 'assignment_scores#leaderboard'
          put ':object_id/bulk_upsert', to: 'assignment_scores#bulk_upsert'
        end
      end
      resources :scoring_templates, only: %i[index show create update destroy] do
        collection do
          patch 'assign_template/:id', to: 'scoring_templates#assign_scoring_template'
        end
      end
      resources :mentoring_groups, only: %i[index create update]
      resources :user_mentoring_groups, only: %i[index]
      resources :behaviours, only: %i[destroy]
      resources :behaviour_scorings, only: %i[index create summary update_score] do
        collection do
          get 'summary', to: 'behaviour_scorings#summary'
          patch 'scoring/:id', to: 'behaviour_scorings#update_score'
        end
      end
      resources :final_scorings, only: %i[index update]
      resources :competencies, only: %i[index create update] do
        get 'sub_competency_count', to: 'competencies#sub_competency_count', on: :collection
      end
      resources :industry_categories, only: %i[index create update]
      resources :user_remarks, only: %i[index create]

      resources :user_documents
      resources :user_events, only: %i[index show create update] do
        collection do
          delete 'event/:event_id', to: 'user_events#destroy_event'
        end
      end

      resources :web_configs, only: :index do
        post 'upsert', to: 'web_configs#bulk_upsert', on: :collection
      end

      namespace :assessment do
        resources :questions, only: :index
        resources :answers, only: :create
        resources :feedbacks, only: :create do
          get 'result', to: 'feedbacks#result', on: :collection
        end

        resources :aspects, only: :index do
          get 'show_options', to: 'aspects#show_options', on: :collection
        end

        resources :items do
          post 'question/convert', to: 'items#convert_into_question', on: :collection
          post 'import/csv', to: 'items#import_csv', on: :collection
        end

        resources :configs, only: :index

        get 'results', to: 'results#result'
        get 'summary', to: 'results#summary'

        get ':course_id/scores', to: 'scores#scores'

        resources :scores, only: :index
        resources :progress, only: :index

        get ':course_id/report', to: 'scores#report'
        post 'start_extra_time', to: 'progress#start_extra_time'

        resources :unconnected, only: :index
      end

      resources :docs, only: [:index]
      resources :locales, only: [:index, :show, :create, :update]
      resources :user_invoices, only: %i[index create show update] do
        patch ':id/expire', to: 'user_invoices#expire', on: :collection
        post 'callback', to: 'user_invoices#handle_callback', on: :collection
        post 'migrate', to: 'user_invoices#migrate', on: :collection
        post 'vwxes', to: 'user_invoices#create_vwx_invoice', on: :collection
        post ':id/notify', to: 'user_invoices#notify', on: :collection
        get ':id/download', to: 'user_invoices#download', on: :collection
      end
      resources :user_installments, only: %i[index show update]
      resources :promo_codes, only: %i[index show create update] do
        post 'redeem', to: 'promo_codes#redeem', on: :collection
        post 'bulk_create', to: 'promo_codes#bulk_create', on: :collection
        get 'validate', to: 'promo_codes#validate', on: :collection
        get 'referral', to: 'promo_codes#referred_users', on: :collection
        get 'list_external', to: 'promo_codes#list_external', on: :collection
        post 'redeem_external', to: 'promo_codes#redeem_external', on: :collection
      end
      resources :installment_requests, only: %i[index create update]

      namespace :virtual_working_experience do
        resources :requests, only: :create
        resources :vwx_rewards
        resources :tasks, only: %i[show create update destroy index] do
          patch '/bulk_update', to: 'tasks#bulk_update', on: :collection
          post ':id/complete', to: 'tasks#mark_as_complete', on: :collection
          get ':vwx_id/task_completion', to: 'tasks#task_completion', on: :collection
          patch ':id/send_reminder', to: 'tasks#send_reminder', on: :collection
        end
        resources :task_components, only: %i[index show create update destroy] do
          post ':id/answer', to: 'task_components#answer', on: :collection
          post ':id/recalculate_exam_score',
               to: 'task_components#recalculate_exam_score',
               on: :collection
        end
        resources :sections, only: %i[show create update destroy]
        resources :talent_access_tokens do
          post 'talent_share_access_token',
               to: 'talent_access_tokens#talent_share_access_token',
               on: :collection
          post 'verify_talent_access_token',
               to: 'talent_access_tokens#verify_talent_access_token',
               on: :collection
        end

        post '/bulk_duplicates', to: 'duplicates#bulk_duplicate'
        get '/bulk_duplicates/:id', to: 'duplicates#show'
      end

      resources :job_roles, only: %i[index show create update]
      resources :vwxes, only: %i[index show create update destroy] do
        post 'enroll', to: 'vwxes#enroll', on: :collection
        post ':id/apply', to: 'vwxes#apply', on: :collection
        post 'send_reminders', to: 'vwxes#send_reminders', on: :collection
        get ':id/certificates', to: 'vwxes#certificate_metadata', on: :collection
        get ':external_id/result', to: 'vwxes#result', on: :collection
        get 'quota', to: 'vwxes#show_user_quota', on: :collection
        get 'competence', to: 'vwxes#show_user_competence', on: :collection
        patch ':id/complete_onboard', to: 'vwxes#complete_onboard', on: :collection

        post 'duplicate', to: 'duplicate'
        post 'archive', to: 'archive'
        post 'complete', to: 'vwxes#complete_vixes', on: :collection
        get 'recommendations', to: 'vwxes#recommendations', on: :collection
      end

      resources :user_questions, only: :create
      resources :rewards
      resources :external_rewards
      resources :course_rewards do
        post ':id/remind_me', to: 'course_rewards#remind_me', on: :collection
      end
      resources :user_rewards, only: %i[index show create]

      resources :job_vacancies do
        get 'statistics', to: 'job_vacancies#statistics', on: :collection
        post ':id/duplicate', to: 'job_vacancies#duplicate', on: :collection
        patch ':id/recalculate_match_progress_percentage',
              to: 'job_vacancies#recalculate_match_progress_percentage',
              on: :collection
        put ':id/share', to: 'job_vacancies#share', on: :collection
        get 'applicant_sources', to: 'job_vacancies#applicant_sources', on: :collection
        get 'insights', to: 'job_vacancies#insights', on: :collection
        get 'recommendations', to: 'job_vacancies#recommendations', on: :collection
        get ':id/match_making_config', to: 'job_vacancies#match_making_config', on: :collection

        resources :column_settings, only: [], controller: :hiring_pipeline_column_settings do
          collection do
            get ':state', action: :show, constraints: { state: %r{[^/]+} }
            get ':state/grouped', action: :show_grouped, constraints: { state: %r{[^/]+} }
            patch ':state', action: :upsert, constraints: { state: %r{[^/]+} }
          end
        end

        resources :column_filters, only: [], controller: :hiring_pipeline_column_filters do
          collection do
            get ':state', action: :index, constraints: { state: %r{[^/]+} }
            post ':state/upsert', action: :upsert, constraints: { state: %r{[^/]+} }
          end
        end

        patch 'mass_updates', to: 'job_vacancies#mass_updates', on: :collection
      end

      resources :user_job_vacancies, only: %i[index create update] do
        patch '/', to: 'user_job_vacancies#bulk_update', on: :collection
        get 'comparison', to: 'user_job_vacancies#comparison', on: :collection
        get 'recruitment_funnels', to: 'user_job_vacancies#recruitment_funnels', on: :collection
        get 'histories', to: 'user_job_vacancies#histories', on: :collection
        get 'applied', to: 'user_job_vacancies#applied_job_vacancies', on: :collection
        get 'page_info', to: 'user_job_vacancies#page_info', on: :collection
        get 'submission_pipelines', to: 'user_job_vacancies#submission_pipelines', on: :collection
      end

      get 'user_vwx_tasks/summary', to: 'user_vwx_tasks#summary'
      resources :education_levels, only: :index
      resources :locations, only: [:index]
      resources :universities, only: [:index]
      resources :university_majors, only: [:index]
      resources :job_offers

      resources :partners, only: [:index, :create, :update] do
        get 'recruiters', to: 'partners#recruiters', on: :collection
      end

      resources :partner_attributes, only: [] do
        get 'all', to: 'partner_attributes#all', on: :collection
      end

      resources :provinces, only: [:index]
      resources :job_role_groups, only: [:index]
      resources :job_guarantees, only: [:index, :update] do
        post ':id/send_reminder', to: 'job_guarantees#send_reminder', on: :collection
        post '/duplicate', to: 'job_guarantees#duplicate', on: :collection
      end
      resources :user_vwxes, only: %i[index update] do
        patch ':vwx_id/withdraw', to: 'user_vwxes#withdraw', on: :collection
        get '/talent_pools', to: 'user_vwxes#list_talent_pools', on: :collection
        get '/saved_candidates_count', to: 'user_vwxes#saved_candidates_count',
                                       on: :collection
        get '/talent_pools/download', to: 'user_vwxes#download_talent_pools', on: :collection
        get ':id/generate_user_cv/', to: 'user_vwxes#generate_user_cv', on: :collection
        get '/talent_competency_details', to: 'user_vwxes#talent_competency_details',
                                          on: :collection
        put '/bulk_update', to: 'user_vwxes#bulk_update', on: :collection
        get '/talent_count', to: 'user_vwxes#talent_count', on: :collection
        get '/all_talent_count', to: 'user_vwxes#all_talent_count', on: :collection
      end

      resources :sub_competencies, only: %i[index]
      resources :bloom_taxonomies, only: %i[index]
      resources :career_paths, only: %i[index] do
        get 'result', to: 'career_paths#result', on: :collection
      end
      resources :user_career_paths, only: %i[index update] do
        post 'select_path', to: 'user_career_paths#select_path', on: :collection
        get ':user_id/percentile_rank', to: 'user_career_paths#percentile_rank', on: :collection
        get 'recommended_talents', to: 'user_career_paths#recommended_talents', on: :collection
      end

      resources :course_vwxes, only: %i[create]

      namespace :homework_question do
        post ':homework_question_id/taggings', to: 'taggings#create'
        resources :taggings, only: %i[index destroy] do
          post ':homework_tagging_id/grade', to: 'taggings#grade', on: :collection
        end
      end

      resources :user_course_counselings, only: %i[index create update]
      resources :partner_teams, only: %i[index create destroy update]
      resources :user_vwx_partners, only: %i[create]
      resources :user_homework_questions, only: %i[create]

      resources :final_scoring_metrics, only: %i[index destroy] do
        put 'bulk_upsert', to: 'final_scoring_metrics#bulk_upsert', on: :collection
      end

      resources :final_scoring_sub_metrics, only: %i[destroy]
      resources :consultation_trainings, only: %i[create]
      resources :consultation_hirings, only: %i[create]
      resources :bundling_codes, only: %i[index create destroy]
      resources :user_preferences, only: %i[update]

      resources :user_groups, only: %i[index create update destroy] do
        get 'count_non_student', to: 'user_groups#count_non_student', on: :collection
      end

      resources :user_tokens, only: [] do
        collection do
          post 'verify', to: 'user_tokens#verify'
          post 'upload_verification', to: 'user_tokens#verify_upload_verification'
          get 'uploaded_document', to: 'user_tokens#uploaded_document'
          get 'vacancy_assessment_invitation', to: 'user_tokens#vacancy_assessment_invitation'
          post 'send_verification', to: 'user_tokens#send_verification'
          post 'send_magic_link', to: 'user_tokens#send_magic_link'
        end
      end

      namespace :ai do
        resources :openai, only: [] do
          collection do
            post 'stream_response', to: 'openai#stream_response'
            get 'chats', to: 'openai#list_chats'
            put 'chats/:chat_id', to: 'openai#update_chat'
            get 'chats/:chat_id', to: 'openai#show_chat'
            get 'chats/:chat_id/messages', to: 'openai#list_messages'

            resources :assistant_runs, only: %i[create show], controller: 'openai/assistant_runs'
          end
        end

        resources :video_analyzer, only: %i[] do
          post 'analyze', to: 'video_analyzer#analyze', on: :collection
          get ':id/transcript', to: 'video_analyzer#transcript', on: :collection
          get ':id/analysis', to: 'video_analyzer#analysis', on: :collection
          get ':id/status', to: 'video_analyzer#status', on: :collection
          get ':id/metadata', to: 'video_analyzer#metadata', on: :collection
        end

        resources :interviews, only: [] do
          collection do
            post 'start', to: 'interviews#start'
            post 'submit', to: 'interviews#submit'
            post 'finish', to: 'interviews#finish'
            get ':chat_id/result', to: 'interviews#result'
            get ':chat_id/progress', to: 'interviews#progress'
          end
        end
      end

      resources :user_notifications, only: %i[index update]
      resources :user_feature_onboardings, only: %i[show update]

      resources :features, only: %i[index create update] do
        get 'latest', to: 'features#latest', on: :collection
        post 'upsert', to: 'features#bulk_upsert', on: :collection
      end

      resources :digital_cvs do
        get 'settings', to: 'digital_cvs#show_settings', on: :collection
        patch 'settings/:id', to: 'digital_cvs#update_settings', on: :collection
        get 'address_data', to: 'digital_cvs#address_data', on: :collection
        get 'visitor_count', to: 'digital_cvs#visitor_count', on: :collection
        get 'similar_role_users', to: 'digital_cvs#similar_role_users', on: :collection
      end

      resources :talent_recommendations, only: %i[index]

      resources :user_competencies, only: [] do
        get 'list_validated', to: 'user_competencies#list_validated', on: :collection
        get 'list_not_validated', to: 'user_competencies#list_not_validated', on: :collection
        get 'list_recommended', to: 'user_competencies#list_recommended', on: :collection
        put 'add_recommended', to: 'user_competencies#add_recommended', on: :collection
      end

      resources :jap_campus do
        get 'check_status', to: 'jap_campuses#check_status', on: :collection
        get 'period', to: 'jap_campuses#period', on: :collection
      end

      resources :company_registration_requests, only: %i[create]
      resources :feature_permissions, only: [] do
        post 'import', to: 'feature_permissions#import', on: :collection
      end

      resources :stimuli, only: %i[index show]
      resources :partner_saved_talents, only: [] do
        collection do
          post ':user_id/add', to: 'partner_saved_talents#add'
          delete ':user_id/remove/:job_vacancy_id', to: 'partner_saved_talents#remove'
        end
      end

      resources :company_assessment_templates, only: %i[index create update] do
        post 'connect', to: 'company_assessment_templates#connect', on: :collection
      end
      resources :job_vacancy_assessments, only: %i[create update destroy index show] do
        get 'integrated_pipeline', to: 'job_vacancy_assessments#verify_pipeline_integrated',
                                   on: :collection
      end
      resources :user_job_vacancy_assessments, only: %i[index update] do
        post ':id/accept', to: 'user_job_vacancy_assessments#accept', on: :collection
        post ':id/reject', to: 'user_job_vacancy_assessments#reject', on: :collection
        post 'verify_tokens', to: 'user_job_vacancy_assessments#verify_tokens', on: :collection
        post 'upload_external_result',
             to: 'user_job_vacancy_assessments#upload_external_result',
             on: :collection
        get 'scores', to: 'user_job_vacancy_assessments#scores', on: :collection
      end
      resources :talent_data_crawling_requests, only: %i[create]
      resources :specializations, only: %i[index]
      resources :program_registrations, only: %i[create] do
        get 'identify', to: 'program_registrations#identify', on: :collection
      end
      resources :user_assessments, only: [] do
        post 'callback/:assessment_service', to: 'user_assessments#callback', on: :collection
      end

      resources :user_proctor_logs, only: %i[index create]
      resources :user_proctor_reports, only: %i[index create]

      post 'report_assessments/upload', to: 'report_assessments#upload'
      get 'features/config', to: 'web_configs#organizations_config'

      resources :user_course_reviews, only: %i[index create] do
        get 'review_statistics/:id', to: 'user_course_reviews#review_statistics', on: :collection
      end

      resources :vacancy_questions, only: %i[index] do
        get ':job_vacancy_id/answers', to: 'vacancy_questions#answers', on: :collection
        get ':id/options', to: 'vacancy_questions#options', on: :collection
        post 'upsert', to: 'vacancy_questions#upsert', on: :collection
        post 'answer', to: 'vacancy_questions#answer', on: :collection
      end

      resources :hiring_processes, only: [] do
        post 'confirm_interview', to: 'hiring_processes#confirm_interview', on: :collection
        post 'confirm_offering', to: 'hiring_processes#confirm_offering', on: :collection
        post 'confirm_medical_checkup',
             to: 'hiring_processes#confirm_medical_checkup',
             on: :collection
      end

      namespace :mixpanel do
        get 'email_trackers/open', to: 'email_trackers#open'
      end

      resources :hiring_pipelines, only: %i[index] do
        post 'bulk_upsert', to: 'hiring_pipelines#bulk_upsert', on: :collection
      end

      resources :prakerja, only: [] do
        post 'validate', to: 'prakerja#validate', on: :collection
        post 'callback', to: 'prakerja#callback', on: :collection
        get 'status/:code', to: 'prakerja#status', on: :collection
      end

      resources :job_vacancy_recruiters, only: [] do
        post 'upsert', to: 'job_vacancy_recruiters#upsert', on: :collection
      end

      namespace :course do
        resources :insights, only: %i[index] do
          collection do
            get 'detail', to: 'insights#detail'
            get 'competency', to: 'insights#competency'
            get 'division', to: 'insights#division'
            get 'download', to: 'insights#download'
          end
        end
      end

      resources :course_configs, only: %i[index]

      resources :user_hiring_pipeline_feedbacks, only: %i[index create update destroy] do
        patch ':id/undiscard', to: 'user_hiring_pipeline_feedbacks#undiscard', on: :collection
      end

      resources :user_hiring_pipeline_submissions, only: [] do
        post 'upload', to: 'user_hiring_pipeline_submissions#upload', on: :collection
      end

      resources :shared_vacancy_tokens, only: %i[create show]
      post 'mailers/send_email', to: 'mailers#send_email'
      resources :user_vacancy_batch_updates, only: %i[index create show]
      resources :invite_assesses, only: %i[index create show]
      resources :mailer_templates, only: %i[index show update] do
        post ':id/send_test_email', to: 'mailer_templates#send_test_email', on: :collection
      end

      resources :hiring_pipeline_column_filters, only: %i[update] do
        get ':id/options', to: 'hiring_pipeline_column_filters#options', on: :collection
      end

      resources :licenses, only: %i[index create]
      resources :partner_placements, only: %i[index create]
      resources :job_vacancy_taggings, only: %i[index create]
      resources :user_vacancy_another_source_imports, only: %i[show create]
      resources :feedback_templates, only: %i[show]
      resources :user_feedbacks, only: %i[create]

      scope :ti_analytics do
        resources :projects, controller: 'ti/analytic_projects'
        get 'variables', to: 'ti/analytic_projects#variables'
        get 'groups', to: 'ti/analytic_projects#groups'
        get 'filters', to: 'ti/analytic_projects#filters'
        get 'talent_distribution', to: 'ti/analytic_projects#talent_distribution'
        get 'comparisons', to: 'ti/analytic_projects#comparisons'
      end

      scope :ti_idp do
        resources :projects, controller: 'ti/idp_projects'
      end

      post 'qiscus/callback/:scheme', to: 'qiscus#callback'
      get '/current_time', to: ->(_env) { rack_json(current_time: Time.current) }

      namespace :ti do
        resources :competencies, only: [] do
          collection do
            get 'distributions', to: 'competencies#distributions'
            get 'overall', to: 'competencies#overall'
            get 'users', to: 'competencies#users'
            get 'heatmap', to: 'competencies#heatmap'
            get 'filters', to: 'competencies#filters'
            patch 'filters', to: 'competencies#upsert_filters'
          end
        end

        resources :talent_gaps, only: [] do
          collection do
            get 'distributions', to: 'talent_gaps#distributions'
            get 'overall', to: 'talent_gaps#overall'
            get 'users', to: 'talent_gaps#users'
          end
        end
      end

      resources :recordings, only: %i[index show create update destroy] do
        collection do
          get ':id/presign_parts', to: 'recordings#presign_parts'
          post ':id/parts', to: 'recordings#add_part'
        end
      end
    end

    namespace :v2 do
      resources :users, only: [:update, :show]
      resources :surveys, only: [:create, :index, :show, :update, :destroy]
    end

    mount Sidekiq::Web => '/sidekiq'
    get '/version', to: 'version#index'
  end

  root to: ->(_env) { rack_json(message: 'How did you get here ?') }
end
